// processUpdateShopifyProductTagsTask.js
// Updates a Shopify product's tags based on t_products data
// Payload contains t_products.id
// Tags are calculated the same way as in publish_product_accessory
// Supports multiple Shopify stores via t_products.shopify_store_id

import { getShopifyCredentials, getProductsBaseUrl, shopifyGraphQLRequest as makeGraphQLRequest } from './shopifyStoreConfig.js';

function extractNumericIdFromGid(gid) {
  if (!gid) return null;
  const parts = String(gid).split('/');
  return parts[parts.length - 1];
}

export default async function processUpdateShopifyProductTagsTask(task, { supabase, updateTaskStatus, logError }) {
  try {
    // Parse payload
    const payload = typeof task.payload === 'object' && task.payload !== null ? task.payload : JSON.parse(task.payload || '{}');
    const productId = payload.product_id || payload.id;

    if (!productId) {
      await updateTaskStatus(task.id, 'error', { message: 'Missing product_id in payload' });
      return;
    }

    await updateTaskStatus(task.id, 'processing');

    // Fetch the product from t_products
    const { data: product, error: prodErr } = await supabase
      .from('t_products')
      .select('id, shopify_handle, shopify_uploaded_at, shopify_store_id, brand_id, category_id')
      .eq('id', productId)
      .maybeSingle();

    if (prodErr) {
      await logError?.(`[processUpdateShopifyProductTagsTask] DB error: ${prodErr.message}`, { productId });
    }

    if (!product) {
      await updateTaskStatus(task.id, 'error', { message: `No t_products row found for id ${productId}` });
      return;
    }

    // Check if product has been uploaded to Shopify
    if (!product.shopify_uploaded_at) {
      await updateTaskStatus(task.id, 'completed', {
        message: 'Product not yet uploaded to Shopify',
        product_id: productId,
        status: 'not_uploaded'
      });
      return;
    }

    // Get store ID (default to 1 for backward compatibility)
    const storeId = product.shopify_store_id || 1;
    let credentials;
    try {
      credentials = getShopifyCredentials(storeId);
    } catch (error) {
      await updateTaskStatus(task.id, 'error', { message: `Failed to get Shopify credentials: ${error.message}` });
      return;
    }

    const { storeName } = credentials;

    // Find Shopify product by handle
    const data = await makeGraphQLRequest(`
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) { id handle title }
      }
    `, { handle: product.shopify_handle }, storeId);

    const shopifyProduct = data?.productByHandle;
    if (!shopifyProduct) {
      await updateTaskStatus(task.id, 'error', { message: `No Shopify product found for handle ${product.shopify_handle} on ${storeName}` });
      return;
    }

    const shopifyProductId = extractNumericIdFromGid(shopifyProduct.id);

    // Calculate tags based on product data (same logic as publish_product_accessory)
    const tags = [];

    // Always include class_accessory
    tags.push('class_accessory');

    // Brand tag: brand_<brand_name>
    if (product.brand_id != null) {
      try {
        const { data: brand } = await supabase
          .from('t_brands')
          .select('brand')
          .eq('id', product.brand_id)
          .maybeSingle();
        if (brand?.brand) {
          tags.push(`brand_${brand.brand}`);
        }
      } catch (e) {
        // ignore brand fetch errors
      }
    }

    // Category tags from shopify_tag
    if (product.category_id != null) {
      try {
        const { data: cat } = await supabase
          .from('t_categories')
          .select('shopify_tag')
          .eq('id', product.category_id)
          .maybeSingle();
        if (cat?.shopify_tag) {
          const categoryTags = cat.shopify_tag.split(',').map(t => t.trim()).filter(t => t);
          tags.push(...categoryTags);
        }
      } catch (e) {
        // ignore category fetch errors
      }
    }

    // Color_Family_* tags from product variants
    const { data: variants } = await supabase
      .from('t_product_variants')
      .select('color_id')
      .eq('product_id', productId);

    if (variants && variants.length > 0) {
      const colorIds = [...new Set(variants.map(v => v.color_id).filter(id => id != null))];
      if (colorIds.length > 0) {
        try {
          const { data: colors } = await supabase
            .from('t_colors')
            .select('id, color')
            .in('id', colorIds);
          if (colors) {
            for (const color of colors) {
              if (color.color) {
                tags.push(`Color_Family_${color.color}`);
              }
            }
          }
        } catch (e) {
          // ignore color fetch errors
        }
      }
    }

    // Update Shopify product tags via REST API
    const { endpoint, accessToken } = credentials;
    const baseUrl = getProductsBaseUrl(endpoint);
    const url = `${baseUrl}/products/${shopifyProductId}.json`;
    const updatePayload = { product: { id: Number(shopifyProductId), tags: tags.join(', ') } };

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken,
      },
      body: JSON.stringify(updatePayload),
    });

    const result = await response.json();
    if (!response.ok) {
      throw new Error(`Error updating product ${shopifyProductId} on ${storeName}: ${JSON.stringify(result)}`);
    }

    await updateTaskStatus(task.id, 'completed', {
      message: `Updated Shopify product tags on ${storeName}`,
      product_id: productId,
      shopify_product_id: shopifyProductId,
      tags_count: tags.length,
      tags: tags,
      store: storeName,
      store_id: storeId,
    });
  } catch (error) {
    await logError?.(`[processUpdateShopifyProductTagsTask] Error: ${error.message}`, { taskId: task?.id });
    await updateTaskStatus(task.id, 'error', { message: 'Error updating Shopify product tags', error: error.message });
  }
}

