// processPublishProductAccessoryTask.js
// Publish an accessory (t_products + t_product_variants) to Shopify as a product with variants

import fetch from 'node-fetch';
import { getShopifyCredentials } from './shopifyStoreConfig.js';

function lbsToGrams(lbs) {
  if (lbs === null || lbs === undefined) return undefined;
  const num = Number(lbs);
  if (Number.isNaN(num)) return undefined;
  return Math.round(num * 453.59237);
}

function sanitizeHandle(handle) {
  if (!handle) return handle;
  let sanitized = String(handle).toLowerCase();
  sanitized = sanitized.replace(/ /g, '-');
  sanitized = sanitized.replace(/\./g, '-');
  sanitized = sanitized.replace(/'/g, '');
  sanitized = sanitized.replace(/\//g, '');
  sanitized = sanitized.replace(/\|/g, '');
  sanitized = sanitized.replace(/&/g, '-');
  sanitized = sanitized.replace(/[()"%#$+=\?!*\[\]{}<>:;,^~`]/g, '');
  sanitized = sanitized.replace(/-+/g, '-');
  sanitized = sanitized.replace(/^-+|-+$/g, '');
  return sanitized;
}

// GraphQL helper for metafield operations
async function shopifyGraphQLRequest(query, variables = {}, storeId = 1) {
  const { endpoint, accessToken } = getShopifyCredentials(storeId);
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': accessToken
    },
    body: JSON.stringify({ query, variables })
  });
  const result = await response.json();
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

// Set variant metafields on Shopify
async function setVariantMetafields(variantGid, variantData, supabase, storeId = 1) {
  const metafields = [];

  // 1. dz_disc_variant.color_family from t_colors.color
  if (variantData.color_id) {
    try {
      const { data: color } = await supabase
        .from('t_colors')
        .select('color')
        .eq('id', variantData.color_id)
        .maybeSingle();

      if (color?.color) {
        metafields.push({
          ownerId: variantGid,
          namespace: 'dz_disc_variant',
          key: 'color_family',
          type: 'single_line_text_field',
          value: color.color
        });
      }
    } catch (e) {
      console.warn(`[setVariantMetafields] Failed to fetch color for variant ${variantData.id}:`, e.message);
    }
  }

  // 2. dz_disc_variant.size from option values
  let sizeValue = null;
  if (variantData.op1_name === 'Size' && variantData.op1_value) {
    sizeValue = variantData.op1_value;
  } else if (variantData.op2_name === 'Size' && variantData.op2_value) {
    sizeValue = variantData.op2_value;
  } else if (variantData.op3_name === 'Size' && variantData.op3_value) {
    sizeValue = variantData.op3_value;
  }

  if (sizeValue) {
    metafields.push({
      ownerId: variantGid,
      namespace: 'dz_disc_variant',
      key: 'size',
      type: 'single_line_text_field',
      value: sizeValue
    });
  }

  // 3. Other metafields from t_variant_attributes
  try {
    const { data: variantAttributes } = await supabase
      .from('t_variant_attributes')
      .select(`
        attribute_value,
        t_category_attributes!inner(
          variant_metafield_on_shopify_name,
          variant_metafield_on_shopify_created_at
        )
      `)
      .eq('variant_id', variantData.id);

    if (variantAttributes) {
      for (const attr of variantAttributes) {
        const categoryAttr = attr.t_category_attributes;

        // Only set metafield if it has been created in Shopify
        if (categoryAttr.variant_metafield_on_shopify_name &&
            categoryAttr.variant_metafield_on_shopify_created_at &&
            attr.attribute_value) {

          // Parse namespace.key from variant_metafield_on_shopify_name
          const metafieldName = categoryAttr.variant_metafield_on_shopify_name;
          const parts = metafieldName.split('.');
          if (parts.length === 2) {
            metafields.push({
              ownerId: variantGid,
              namespace: parts[0],
              key: parts[1],
              type: 'single_line_text_field',
              value: attr.attribute_value
            });
          }
        }
      }
    }
  } catch (e) {
    console.warn(`[setVariantMetafields] Failed to fetch variant attributes for variant ${variantData.id}:`, e.message);
  }

  // Set metafields if any exist
  if (metafields.length > 0) {
    const mutation = `
      mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields { id key namespace value }
          userErrors { field message }
        }
      }
    `;

    const variables = { metafields };
    const data = await shopifyGraphQLRequest(mutation, variables, storeId);
    const errors = data?.metafieldsSet?.userErrors;

    if (errors && errors.length > 0) {
      throw new Error(`metafieldsSet userErrors: ${JSON.stringify(errors)}`);
    }

    console.log(`[setVariantMetafields] Set ${metafields.length} metafields for variant ${variantData.id}`);
    return data.metafieldsSet.metafields;
  }

  return [];
}

async function createShopifyProduct(productPayload, storeId = 1) {
  const { endpoint, accessToken } = getShopifyCredentials(storeId);
  const productsEndpoint = endpoint.replace('/graphql.json', '/products.json');

  const resp = await fetch(productsEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': accessToken
    },
    body: JSON.stringify({ product: productPayload })
  });

  if (!resp.ok) {
    const text = await resp.text();
    throw new Error(`Shopify create product failed: ${resp.status} ${resp.statusText} - ${text}`);
  }

  const json = await resp.json();
  return json?.product;
}

async function getShopifyProduct(productId, handle = null, storeId = 1) {
  const { endpoint, accessToken } = getShopifyCredentials(storeId);

  if (handle) {
    // Look up by handle using GraphQL
    const query = `
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) {
          id
          handle
          title
          variants(first: 250) {
            edges {
              node {
                id
                sku
              }
            }
          }
          images(first: 250) {
            edges {
              node {
                id
                src
              }
            }
          }
        }
      }
    `;

    const graphqlResponse = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables: { handle } })
    });

    if (!graphqlResponse.ok) {
      throw new Error(`Failed to query Shopify GraphQL: ${graphqlResponse.status} ${graphqlResponse.statusText}`);
    }

    const graphqlData = await graphqlResponse.json();
    if (graphqlData.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(graphqlData.errors)}`);
    }

    const product = graphqlData.data?.productByHandle;
    if (!product) {
      return null; // Product not found
    }

    // Convert GraphQL format to REST format for consistency
    return {
      id: product.id.replace('gid://shopify/Product/', ''),
      handle: product.handle,
      title: product.title,
      variants: product.variants.edges.map(edge => ({
        id: edge.node.id.replace('gid://shopify/ProductVariant/', ''),
        sku: edge.node.sku
      })),
      images: product.images.edges.map(edge => ({
        id: edge.node.id.replace('gid://shopify/ProductImage/', ''),
        src: edge.node.src
      }))
    };
  } else {
    // Look up by ID using REST
    const productsEndpoint = endpoint.replace('/graphql.json', '/products.json');
    const base = productsEndpoint.split('/products.json')[0];
    const url = `${base}/products/${productId}.json`;
    const resp = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken
      }
    });
    if (!resp.ok) {
      const t = await resp.text();
      throw new Error(`GET product ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
    }
    const j = await resp.json();
    return j?.product;
  }
}

async function updateVariantImage(variantId, imageId, storeId = 1) {
  const { endpoint, accessToken } = getShopifyCredentials(storeId);
  const productsEndpoint = endpoint.replace('/graphql.json', '/products.json');
  const base = productsEndpoint.split('/products.json')[0];
  const url = `${base}/variants/${variantId}.json`;
  const body = { variant: { id: variantId, image_id: imageId } };
  const resp = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': accessToken
    },
    body: JSON.stringify(body)
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`PUT variant ${variantId} image failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j?.variant;
}


export default async function processPublishProductAccessoryTask(
  task,
  { supabase, updateTaskStatus, logError } = {}
) {
  try {
    // Parse payload (object or string JSON)
    let payload;
    try {
      if (typeof task.payload === 'object' && task.payload !== null) {
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload || '{}');
      } else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processPublishProductAccessoryTask] Invalid payload: ${err.message}`;
      console.error(errMsg);
      if (logError) await logError(errMsg, `task ${task.id}`);
      if (updateTaskStatus) await updateTaskStatus(task.id, 'error', { message: 'Invalid JSON payload', error: err.message });
      return;
    }

    const productId = payload.id;
    const storeId = payload.shopify_store_id || 1; // Default to DZ Discs for backward compatibility
    const { storeName } = getShopifyCredentials(storeId);

    if (!productId) {
      const errMsg = `[processPublishProductAccessoryTask] Missing id in payload`;
      console.error(errMsg);
      if (logError) await logError(errMsg, `task ${task.id}`);
      if (updateTaskStatus) await updateTaskStatus(task.id, 'error', { message: 'Missing id in payload', store: storeName, store_id: storeId });
      return;
    }

    // Load product
    const { data: product, error: prodErr } = await supabase
      .from('t_products')
      .select('id, brand_id, category_id, name, description, shopify_handle, shopify_product_template, todo, shopify_uploaded_at')
      .eq('id', productId)
      .maybeSingle();

    if (prodErr) {
      const errMsg = `[processPublishProductAccessoryTask] DB error fetching t_products ${productId}: ${prodErr.message}`;
      console.error(errMsg);
      if (logError) await logError(errMsg, `task ${task.id}`);
      if (updateTaskStatus) await updateTaskStatus(task.id, 'error', { message: 'DB error fetching product', error: prodErr.message });
      return;
    }
    if (!product) {
      const errMsg = `[processPublishProductAccessoryTask] Product ${productId} not found`;
      console.error(errMsg);
      if (logError) await logError(errMsg, `task ${task.id}`);
      if (updateTaskStatus) await updateTaskStatus(task.id, 'error', { message: 'Product not found' });
      return;
    }

    // Must have todo = Ready to Publish
    const todo = (product.todo || '').trim();
    const isReady = /^ready to publish$/i.test(todo);
    if (!isReady) {
      if (updateTaskStatus) await updateTaskStatus(task.id, 'completed', { status: 'product_not_ready', message: 'product not ready', product_id: productId, todo: product.todo });
      return;
    }

    await updateTaskStatus(task.id, 'processing');

    // Fetch a candidate ready variant
    const { data: candidateVariant, error: varErr } = await supabase
      .from('t_product_variants')
      .select('id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value, price, "UPC", msrp, shopify_weight_lbs, stock_quantity, order_cost, color_id, todo, uploaded_to_shopify_at')
      .eq('product_id', productId)
      .order('id', { ascending: true })
      .limit(1)
      .maybeSingle();

    // If no variant or not ready, enqueue readiness check and complete
    let foundReadyVariant = null;
    if (!varErr && candidateVariant && /^ready to publish$/i.test(String(candidateVariant.todo || '').trim())) {
      foundReadyVariant = candidateVariant;
    } else {
      // Try to find any ready variant
      const { data: anyReady, error: readyErr } = await supabase
        .from('t_product_variants')
        .select('id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value, price, "UPC", msrp, shopify_weight_lbs, stock_quantity, order_cost, color_id, todo, uploaded_to_shopify_at')
        .eq('product_id', productId);
      if (!readyErr && Array.isArray(anyReady)) {
        foundReadyVariant = anyReady.find(v => /^ready to publish$/i.test(String(v.todo || '').trim())) || null;
      }
    }

    if (!foundReadyVariant) {
      // Enqueue a product readiness recheck
      const nowIso = new Date().toISOString();
      try {
        const { error: insErr } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'check_if_product_is_ready',
            payload: { id: productId },
            status: 'pending',
            scheduled_at: nowIso,
            created_at: nowIso,
            enqueued_by: 'worker:publish_product_accessory'
          });
        if (insErr) {
          await logError?.(`[processPublishProductAccessoryTask] Failed to enqueue check_if_product_is_ready for product ${productId}: ${insErr.message}`, `task ${task.id}`);
        }
      } catch (e) {
        await logError?.(`[processPublishProductAccessoryTask] Exception enqueueing check_if_product_is_ready: ${e.message}`, `task ${task.id}`);
      }

      await updateTaskStatus(task.id, 'completed', { status: 'product_not_ready', message: 'product not ready', product_id: productId });
      return;
    }

    // Build options from ready variants of this product (all that are ready)
    const { data: readyVariantsAll } = await supabase
      .from('t_product_variants')
      .select('id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value, price, "UPC", msrp, shopify_weight_lbs, stock_quantity, order_cost, color_id, todo, uploaded_to_shopify_at')
      .eq('product_id', productId);

    const readyVariants = (readyVariantsAll || []).filter(v => /^ready to publish$/i.test(String(v.todo || '').trim()));
    const variantsToPublish = readyVariants.length > 0 ? readyVariants : [foundReadyVariant];

    // Derive option names from the candidate variant
    const optNames = [];
    const n1 = foundReadyVariant.op1_name?.trim();
    const n2 = foundReadyVariant.op2_name?.trim();
    const n3 = foundReadyVariant.op3_name?.trim();
    if (n1) optNames.push(n1); if (n2) optNames.push(n2); if (n3) optNames.push(n3);
    if (optNames.length === 0) optNames.push('Title');

    const options = optNames.map(name => ({ name }));

    // Map variants
    const shopifyVariants = variantsToPublish.map(v => {
      const grams = lbsToGrams(v.shopify_weight_lbs);
      const optionValues = [];
      if (n1) optionValues.push(v.op1_value || '');
      if (n2) optionValues.push(v.op2_value || '');
      if (n3) optionValues.push(v.op3_value || '');
      if (optionValues.length === 0) optionValues.push('Default Title');

      return {
        sku: `DGACC${v.id}`,
        price: v.price != null ? String(v.price) : undefined,
        compare_at_price: v.msrp != null ? String(v.msrp) : undefined,
        barcode: v.UPC || undefined,
        grams: grams,
        inventory_management: 'shopify',
        inventory_policy: 'deny',
        fulfillment_service: 'manual',
        requires_shipping: true,
        inventory_quantity: (v.stock_quantity ?? 0),
        option1: optionValues[0] || null,
        option2: optionValues[1] || null,
        option3: optionValues[2] || null
      };
    });

    // Fetch brand name for vendor
    let vendor = undefined;
    if (product.brand_id != null) {
      try {
        const { data: brand } = await supabase
          .from('t_brands')
          .select('brand')
          .eq('id', product.brand_id)
          .maybeSingle();
        vendor = brand?.brand || undefined;
      } catch (e) {
        // ignore vendor if fetch fails
      }
    }

    // Fetch category for product_type and tags
    let productType = undefined;
    let categoryTags = [];
    if (product.category_id != null) {
      try {
        const { data: cat } = await supabase
          .from('t_categories')
          .select('name, shopify_type, shopify_tag')
          .eq('id', product.category_id)
          .maybeSingle();
        console.log(`[processPublishProductAccessoryTask] Category fetch result:`, cat);
        if (cat) {
          productType = cat.shopify_type || cat.name || undefined;
          if (cat.shopify_tag) {
            // shopify_tag might be comma-separated list
            categoryTags = cat.shopify_tag.split(',').map(t => t.trim()).filter(t => t);
            console.log(`[processPublishProductAccessoryTask] Parsed category tags:`, categoryTags);
          } else {
            console.log(`[processPublishProductAccessoryTask] No shopify_tag found in category`);
          }
        } else {
          console.log(`[processPublishProductAccessoryTask] No category found for id ${product.category_id}`);
        }
      } catch (e) {
        // ignore
      }
    }

    // Fetch image config and build images for variants
    let publicImageServer, folderProductVariants;
    try {
      const { data: cfg, error: cfgErr } = await supabase
        .from('t_config')
        .select('key, value')
        .in('key', ['public_image_server', 'folder_product_variants']);
      if (!cfgErr && Array.isArray(cfg)) {
        publicImageServer = cfg.find(r => r.key === 'public_image_server')?.value;
        folderProductVariants = cfg.find(r => r.key === 'folder_product_variants')?.value;
      }
    } catch {}

    // Build tags
    const tags = [];

    // Always include class_accessory
    tags.push('class_accessory');

    // Brand tag: brand_<brand_name>
    if (vendor) {
      tags.push(`brand_${vendor}`);
    }

    // Category tags from shopify_tag
    console.log(`[processPublishProductAccessoryTask] Adding category tags to final array:`, categoryTags);
    tags.push(...categoryTags);
    console.log(`[processPublishProductAccessoryTask] Tags after adding category tags:`, tags);

    // Color_Family_* tags from variants
    const colorIds = [...new Set(variantsToPublish.map(v => v.color_id).filter(id => id != null))];
    if (colorIds.length > 0) {
      try {
        const { data: colors } = await supabase
          .from('t_colors')
          .select('id, color')
          .in('id', colorIds);
        if (colors) {
          for (const color of colors) {
            if (color.color) {
              tags.push(`Color_Family_${color.color}`);
            }
          }
        }
      } catch (e) {
        // ignore color fetch errors
      }
    }

    // 30ok_Y tag if any variant has order_cost/price < 0.61
    const has30okVariant = variantsToPublish.some(v => {
      if (v.order_cost != null && v.price != null && v.price > 0) {
        return (v.order_cost / v.price) < 0.61;
      }
      return false;
    });
    if (has30okVariant) {
      tags.push('30ok_Y');
    }

    let images = undefined;
    if (publicImageServer && folderProductVariants) {
      images = variantsToPublish.map(v => ({ src: `${publicImageServer}/${folderProductVariants}/${v.id}.jpg` }));
    }

    // Build product payload
    const productPayload = {
      title: vendor ? `${vendor} ${product.name}` : product.name,
      body_html: product.description || '',
      vendor,
      product_type: productType,
      handle: sanitizeHandle(product.shopify_handle) || undefined,
      template_suffix: product.shopify_product_template || undefined,
      tags: tags.join(', '),
      options,
      variants: shopifyVariants,
      images: images || undefined
    };

    // Check if handle already exists on Shopify
    if (productPayload.handle) {
      try {
        const existingProduct = await getShopifyProduct(null, productPayload.handle, storeId);
        if (existingProduct) {
          await updateTaskStatus(task.id, 'failed', {
            message: `Product handle '${productPayload.handle}' already exists on Shopify on ${storeName}`,
            existing_product_id: existingProduct.id,
            existing_product_title: existingProduct.title,
            product_id: productId,
            store: storeName,
            store_id: storeId
          });
          return;
        }
      } catch (e) {
        // If getShopifyProduct throws an error, it likely means the product doesn't exist, so continue
        console.log(`[processPublishProductAccessoryTask] Handle check failed on ${storeName} (assuming handle is available): ${e.message}`);
      }
    }

    // Create on Shopify
    const created = await createShopifyProduct(productPayload, storeId);
    const createdProductId = created?.id;

    // Set inventory quantities explicitly using inventory levels API
    if (createdProductId && variantsToPublish.length > 0) {
      console.log(`[processPublishProductAccessoryTask] Setting inventory for ${variantsToPublish.length} variants on ${storeName}`);
      try {
        // Get the created product with variants
        const createdFull = await getShopifyProduct(createdProductId, null, storeId);
        const vars = createdFull?.variants || [];
        console.log(`[processPublishProductAccessoryTask] Found ${vars.length} variants in created product on ${storeName}`);

        // Use hardcoded primary location ID to bypass 403 permissions issue
        const { endpoint } = getShopifyCredentials(storeId);
        const productsEndpoint = endpoint.replace('/graphql.json', '/products.json');
        const base = productsEndpoint.split('/products.json')[0];
        const primaryLocation = { id: 63618220220, name: 'Primary Location' };
        console.log(`[processPublishProductAccessoryTask] Using hardcoded primary location: ${primaryLocation.name} (ID: ${primaryLocation.id})`);

        if (primaryLocation) {
          // Set inventory for each variant
          for (const v of variantsToPublish) {
            console.log(`[processPublishProductAccessoryTask] Processing variant ${v.id} with stock_quantity: ${v.stock_quantity}`);
            if (v.stock_quantity != null && v.stock_quantity > 0) {
              const sku = `DGACC${v.id}`;
              const createdVar = vars.find(c => c.sku === sku);
              console.log(`[processPublishProductAccessoryTask] Found created variant for SKU ${sku}: ${createdVar ? 'YES' : 'NO'}`);

              if (createdVar && createdVar.inventory_item_id) {
                console.log(`[processPublishProductAccessoryTask] Setting inventory: location_id=${primaryLocation.id}, inventory_item_id=${createdVar.inventory_item_id}, available=${v.stock_quantity}`);
                try {
                  const inventoryUrl = `${base}/inventory_levels/set.json`;
                  const inventoryResp = await fetch(inventoryUrl, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'X-Shopify-Access-Token': shopifyAccessToken
                    },
                    body: JSON.stringify({
                      location_id: primaryLocation.id,
                      inventory_item_id: createdVar.inventory_item_id,
                      available: v.stock_quantity
                    })
                  });

                  if (inventoryResp.ok) {
                    console.log(`[processPublishProductAccessoryTask] Successfully set inventory for variant ${createdVar.id} to ${v.stock_quantity}`);
                  } else {
                    const errorText = await inventoryResp.text();
                    console.warn(`[processPublishProductAccessoryTask] Failed to set inventory for variant ${createdVar.id}: ${inventoryResp.status} ${inventoryResp.statusText} - ${errorText}`);
                  }
                } catch (e) {
                  console.warn(`[processPublishProductAccessoryTask] Failed to set inventory for variant ${v.id}: ${e.message}`);
                }
              } else {
                console.warn(`[processPublishProductAccessoryTask] Missing created variant or inventory_item_id for variant ${v.id}`);
              }
            } else {
              console.log(`[processPublishProductAccessoryTask] Skipping inventory for variant ${v.id} (stock_quantity: ${v.stock_quantity})`);
            }
          }
        } else {
          console.warn(`[processPublishProductAccessoryTask] No primary location found`);
        }
      } catch (e) {
        console.warn(`[processPublishProductAccessoryTask] Failed to set inventory quantities: ${e.message}`);
      }
    }

    // Associate variant images to variants; fail task if any image cannot be uploaded/linked
    if (createdProductId) {
      if (!publicImageServer || !folderProductVariants) {
        await updateTaskStatus(task.id, 'error', {
          message: 'Missing image configuration (public_image_server or folder_product_variants). Product was created without images.',
          shopify_product_id: createdProductId
        });
        return;
      }

      const failures = [];
      try {
        const createdFull = await getShopifyProduct(createdProductId, null, storeId);
        const imgs = createdFull?.images || [];
        const vars = createdFull?.variants || [];

        for (const v of variantsToPublish) {
          const sku = `DGACC${v.id}`;
          const createdVar = vars.find(c => c.sku === sku);
          const expectedUrl = `${publicImageServer}/${folderProductVariants}/${v.id}.jpg`;

          // Match by filename because Shopify rewrites to CDN URLs
          let matchedImage = imgs.find(im => {
            const src = im.src || '';
            const suffix = `/${v.id}.jpg`;
            return src.endsWith(suffix) || src.includes(suffix);
          });

          if (!matchedImage) {
            // Upload the image to the product and use returned id
            const { endpoint, accessToken } = getShopifyCredentials(storeId);
            const productsEndpoint = endpoint.replace('/graphql.json', '/products.json');
            const base = productsEndpoint.split('/products.json')[0];
            const imageUploadUrl = `${base}/products/${createdProductId}/images.json`;
            const imageResp = await fetch(imageUploadUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': accessToken
              },
              body: JSON.stringify({ image: { src: expectedUrl } })
            });
            if (!imageResp.ok) {
              const errorText = await imageResp.text();
              failures.push({ variant_id: v.id, reason: `Image upload failed: ${imageResp.status} ${imageResp.statusText} - ${errorText}` });
              continue;
            }
            const imageData = await imageResp.json();
            const imgId = imageData.image?.id;
            if (!imgId) {
              failures.push({ variant_id: v.id, reason: 'Image upload succeeded but no image id returned by Shopify' });
              continue;
            }
            matchedImage = { id: imgId };
          }

          if (createdVar && matchedImage?.id) {
            try {
              await updateVariantImage(createdVar.id, matchedImage.id, storeId);
            } catch (e) {
              failures.push({ variant_id: v.id, reason: `Failed to set image for variant ${createdVar.id} on ${storeName}: ${e.message}` });
            }
          } else {
            failures.push({ variant_id: v.id, reason: 'Could not find created variant or image id missing' });
          }
        }
      } catch (e) {
        await updateTaskStatus(task.id, 'error', {
          message: `Failed during variant image association phase on ${storeName}`,
          error: e.message,
          shopify_product_id: createdProductId,
          store: storeName,
          store_id: storeId
        });
        return;
      }

      if (failures.length > 0) {
        await updateTaskStatus(task.id, 'error', {
          message: 'Accessory product created but one or more variant images failed to upload/associate',
          failures,
          shopify_product_id: createdProductId
        });
        return;
      }
    }

    // Set variant metafields
    if (createdProductId && variantsToPublish.length > 0) {
      console.log(`[processPublishProductAccessoryTask] Setting metafields for ${variantsToPublish.length} variants on ${storeName}`);
      try {
        // Get the created product with variants to get Shopify variant IDs
        const createdFull = await getShopifyProduct(createdProductId, null, storeId);
        const vars = createdFull?.variants || [];

        const metafieldErrors = [];

        for (const v of variantsToPublish) {
          const sku = `DGACC${v.id}`;
          const createdVar = vars.find(c => c.sku === sku);

          if (createdVar && createdVar.id) {
            try {
              const variantGid = `gid://shopify/ProductVariant/${createdVar.id}`;
              await setVariantMetafields(variantGid, v, supabase, storeId);
            } catch (e) {
              const errorMsg = `Failed to set metafields for variant ${v.id} (SKU: ${sku}): ${e.message}`;
              console.error(`[processPublishProductAccessoryTask] ${errorMsg}`);
              metafieldErrors.push(errorMsg);
            }
          } else {
            const errorMsg = `Could not find created variant for SKU ${sku}`;
            console.error(`[processPublishProductAccessoryTask] ${errorMsg}`);
            metafieldErrors.push(errorMsg);
          }
        }

        // If any metafield errors occurred, fail the task
        if (metafieldErrors.length > 0) {
          await updateTaskStatus(task.id, 'error', {
            message: `Product published to Shopify on ${storeName} but metafield setting failed`,
            product_id: productId,
            shopify_product_id: createdProductId,
            variants_published: variantIds,
            metafield_errors: metafieldErrors,
            store: storeName,
            store_id: storeId
          });
          return;
        }

      } catch (e) {
        const errorMsg = `Failed to set variant metafields: ${e.message}`;
        console.error(`[processPublishProductAccessoryTask] ${errorMsg}`);
        await updateTaskStatus(task.id, 'error', {
          message: `Product published to Shopify on ${storeName} but metafield setting failed`,
          product_id: productId,
          shopify_product_id: createdProductId,
          metafield_error: errorMsg,
          store: storeName,
          store_id: storeId
        });
        return;
      }
    }

    // Stamp uploaded flags
    const nowIso = new Date().toISOString();
    try {
      const { error: prodUpdErr } = await supabase
        .from('t_products')
        .update({ shopify_uploaded_at: nowIso })
        .eq('id', productId);
      if (prodUpdErr) console.warn(`[processPublishProductAccessoryTask] Failed to stamp t_products ${productId}: ${prodUpdErr.message}`);
    } catch {}

    // Stamp each variant we published
    const variantIds = variantsToPublish.map(v => v.id);
    if (variantIds.length > 0) {
      try {
        const { error: varUpdErr } = await supabase
          .from('t_product_variants')
          .update({ uploaded_to_shopify_at: nowIso })
          .in('id', variantIds);
        if (varUpdErr) console.warn(`[processPublishProductAccessoryTask] Failed to stamp variants ${variantIds.join(', ')}: ${varUpdErr.message}`);
      } catch {}
    }

    // Complete
    await updateTaskStatus(task.id, 'completed', {
      message: `Accessory product published to Shopify on ${storeName}`,
      product_id: productId,
      shopify_product_id: createdProductId,
      variants_published: variantIds,
      store: storeName,
      store_id: storeId
    });
  } catch (err) {
    const storeId = payload?.shopify_store_id || 1;
    const { storeName } = getShopifyCredentials(storeId);

    const msg = `[processPublishProductAccessoryTask] ${err.message}`;
    console.error(msg);
    if (logError) await logError(msg, `task ${task.id}`);

    // Optional: if rate limited, retry once in 5 minutes
    if (/429|rate limit/i.test(err.message)) {
      try {
        const retryAt = new Date(Date.now() + 5 * 60 * 1000).toISOString();
        const { error: resErr } = await supabase
          .from('t_task_queue')
          .update({ status: 'pending', scheduled_at: retryAt, locked_at: null, locked_by: null })
          .eq('id', task.id);
        if (resErr) throw new Error(resErr.message);
        return; // leave as rescheduled
      } catch (e2) {
        // fall through to error completion
      }
    }

    if (updateTaskStatus) await updateTaskStatus(task.id, 'error', { message: `Failed to publish accessory product on ${storeName}`, error: err.message, store: storeName, store_id: storeId });
  }
}

