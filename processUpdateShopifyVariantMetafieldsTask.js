/**
 * Process update_shopify_variant_metafields task
 * 
 * Updates metafields for a Shopify variant that has already been uploaded to Shopify.
 * Payload: { variant_id: 4902 }
 */

import fetch from 'node-fetch';
import { getShopifyCredentials, shopifyGraphQLRequest } from './shopifyStoreConfig.js';

/**
 * Find a product variant by SKU using GraphQL
 * @param {string} sku - The SKU to search for
 * @param {number} storeId - The Shopify store ID (1 = DZ Discs, 2 = Tippmann Parts)
 * @returns {Promise<Object|null>} - The variant information or null if not found
 */
async function findVariantBySku(sku, storeId = 1) {
  try {
    const { storeName } = getShopifyCredentials(storeId);
    console.log(`[processUpdateShopifyVariantMetafieldsTask] Finding variant by SKU: ${sku} on ${storeName}`);

    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const data = await shopifyGraphQLRequest(query, variables, storeId);

    if (!data.productVariants.edges.length) {
      console.log(`[processUpdateShopifyVariantMetafieldsTask] No variant found with SKU: ${sku} on ${storeName}`);
      return null;
    }

    const variant = data.productVariants.edges[0].node;

    return {
      variantGid: variant.id, // Full GraphQL ID like gid://shopify/ProductVariant/123
      variantId: variant.id.split('/').pop(), // Numeric ID
      sku: variant.sku,
      productId: variant.product.id.split('/').pop(),
      productTitle: variant.product.title
    };
  } catch (error) {
    console.error(`[processUpdateShopifyVariantMetafieldsTask] Error finding variant by SKU ${sku}:`, error);
    throw error;
  }
}

/**
 * Set variant metafields on Shopify
 * @param {string} variantGid - The variant GraphQL ID (gid://shopify/ProductVariant/123)
 * @param {Object} variantData - The variant data from database
 * @param {Object} supabase - Supabase client
 * @param {number} storeId - The Shopify store ID
 * @returns {Promise<Array>} - Array of set metafields
 */
async function setVariantMetafields(variantGid, variantData, supabase, storeId = 1) {
  const metafields = [];

  // 1. dz_disc_variant.color_family from t_colors.color
  if (variantData.color_id) {
    try {
      const { data: color } = await supabase
        .from('t_colors')
        .select('color')
        .eq('id', variantData.color_id)
        .maybeSingle();

      if (color?.color) {
        metafields.push({
          ownerId: variantGid,
          namespace: 'dz_disc_variant',
          key: 'color_family',
          type: 'single_line_text_field',
          value: color.color
        });
      }
    } catch (e) {
      console.warn(`[setVariantMetafields] Failed to fetch color for variant ${variantData.id}:`, e.message);
    }
  }

  // 2. dz_disc_variant.size from option values
  let sizeValue = null;
  if (variantData.op1_name === 'Size' && variantData.op1_value) {
    sizeValue = variantData.op1_value;
  } else if (variantData.op2_name === 'Size' && variantData.op2_value) {
    sizeValue = variantData.op2_value;
  } else if (variantData.op3_name === 'Size' && variantData.op3_value) {
    sizeValue = variantData.op3_value;
  }

  if (sizeValue) {
    metafields.push({
      ownerId: variantGid,
      namespace: 'dz_disc_variant',
      key: 'size',
      type: 'single_line_text_field',
      value: sizeValue
    });
  }

  // 3. Other metafields from t_variant_attributes
  try {
    const { data: variantAttributes } = await supabase
      .from('t_variant_attributes')
      .select(`
        attribute_value,
        t_category_attributes!inner(
          variant_metafield_on_shopify_name,
          variant_metafield_on_shopify_created_at
        )
      `)
      .eq('variant_id', variantData.id);

    if (variantAttributes) {
      for (const attr of variantAttributes) {
        const categoryAttr = attr.t_category_attributes;

        // Only set metafield if it has been created in Shopify
        if (categoryAttr.variant_metafield_on_shopify_name &&
            categoryAttr.variant_metafield_on_shopify_created_at &&
            attr.attribute_value) {

          // Parse namespace.key from variant_metafield_on_shopify_name
          const metafieldName = categoryAttr.variant_metafield_on_shopify_name;
          const parts = metafieldName.split('.');
          if (parts.length === 2) {
            metafields.push({
              ownerId: variantGid,
              namespace: parts[0],
              key: parts[1],
              type: 'single_line_text_field',
              value: attr.attribute_value
            });
          }
        }
      }
    }
  } catch (e) {
    console.warn(`[setVariantMetafields] Failed to fetch variant attributes for variant ${variantData.id}:`, e.message);
  }

  // Set metafields if any exist
  if (metafields.length > 0) {
    console.log(`[setVariantMetafields] Attempting to set ${metafields.length} metafields for variant ${variantData.id}`);

    const mutation = `
      mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields { id key namespace value }
          userErrors { field message }
        }
      }
    `;

    const variables = { metafields };

    try {
      const data = await shopifyGraphQLRequest(mutation, variables, storeId);
      const errors = data?.metafieldsSet?.userErrors;
      const setMetafields = data?.metafieldsSet?.metafields;

      // Check for GraphQL user errors
      if (errors && errors.length > 0) {
        const errorMessages = errors.map(err => `${err.field}: ${err.message}`).join('; ');
        throw new Error(`Shopify metafieldsSet userErrors: ${errorMessages}`);
      }

      // Check if we got back the expected number of metafields
      if (!setMetafields || setMetafields.length === 0) {
        throw new Error(`No metafields were set by Shopify. Expected ${metafields.length} metafields but got 0.`);
      }

      // Check for partial updates - this is an error condition
      if (setMetafields.length !== metafields.length) {
        throw new Error(`Partial metafield update: Expected to set ${metafields.length} metafields but only ${setMetafields.length} were confirmed by Shopify. Some metafields failed to update.`);
      }

      console.log(`[setVariantMetafields] Successfully set ${setMetafields.length} metafields for variant ${variantData.id}`);
      return setMetafields;

    } catch (error) {
      console.error(`[setVariantMetafields] Failed to set metafields for variant ${variantData.id}:`, error.message);
      throw new Error(`Failed to set metafields on Shopify: ${error.message}`);
    }
  }

  // If no metafields to set, return empty array (will be handled as error by caller)
  console.log(`[setVariantMetafields] No metafields found to set for variant ${variantData.id} - no color_id, size options, or variant attributes with Shopify metafields`);
  return [];
}

/**
 * Main task handler for update_shopify_variant_metafields
 * @param {Object} task - The task object from t_task_queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
export default async function processUpdateShopifyVariantMetafieldsTask(task, { supabase, updateTaskStatus, logError }) {
  try {
    // Parse payload
    let payload;
    if (typeof task.payload === 'string') {
      payload = JSON.parse(task.payload);
    } else {
      payload = task.payload;
    }

    const variantId = payload.variant_id;
    const storeId = payload.shopify_store_id || 1; // Default to DZ Discs for backward compatibility
    const { storeName } = getShopifyCredentials(storeId);

    if (!variantId) {
      await updateTaskStatus(task.id, 'error', {
        message: 'Missing variant_id in payload',
        store: storeName,
        store_id: storeId
      });
      return;
    }

    console.log(`[processUpdateShopifyVariantMetafieldsTask] Processing variant ID: ${variantId} on ${storeName}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the variant from the database
    const { data: variant, error: variantError } = await supabase
      .from('t_product_variants')
      .select(`
        id, color_id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value,
        uploaded_to_shopify_at, sku_shopify
      `)
      .eq('id', variantId)
      .maybeSingle();

    if (variantError) {
      await updateTaskStatus(task.id, 'error', {
        message: `Database error fetching variant: ${variantError.message}`,
        variantId: variantId,
        store: storeName,
        store_id: storeId
      });
      return;
    }

    if (!variant) {
      await updateTaskStatus(task.id, 'error', {
        message: `Variant not found in database`,
        variantId: variantId,
        store: storeName,
        store_id: storeId
      });
      return;
    }

    // Check if variant has been uploaded to Shopify
    if (!variant.uploaded_to_shopify_at) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Variant has not been uploaded to Shopify yet, skipping metafields update`,
        variantId: variantId,
        uploaded_to_shopify_at: variant.uploaded_to_shopify_at,
        store: storeName,
        store_id: storeId
      });
      return;
    }

    console.log(`[processUpdateShopifyVariantMetafieldsTask] Variant ${variantId} was uploaded to Shopify at ${variant.uploaded_to_shopify_at} on ${storeName}, proceeding with metafields update`);

    // Generate the SKU for this variant (use sku_shopify if available, otherwise fallback to DGACC pattern)
    const variantSku = variant.sku_shopify || `DGACC${variantId}`;

    // Find the variant on Shopify
    const shopifyVariant = await findVariantBySku(variantSku, storeId);
    if (!shopifyVariant) {
      await updateTaskStatus(task.id, 'error', {
        message: `Variant not found on Shopify on ${storeName}`,
        variantId: variantId,
        sku: variantSku,
        store: storeName,
        store_id: storeId
      });
      return;
    }

    console.log(`[processUpdateShopifyVariantMetafieldsTask] Found variant on Shopify: ${shopifyVariant.variantGid}`);

    // Update the variant metafields
    const updatedMetafields = await setVariantMetafields(shopifyVariant.variantGid, variant, supabase, storeId);

    // If no metafields were found to set, that's normal - complete the task
    if (!updatedMetafields || updatedMetafields.length === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `No metafields found to set for variant ${variantId} on ${storeName}. Variant has no color_id, size options, or variant attributes with Shopify metafields configured.`,
        variantId: variantId,
        sku: variantSku,
        shopify_variant_id: shopifyVariant.variantId,
        product_title: shopifyVariant.productTitle,
        variant_color_id: variant.color_id,
        variant_options: {
          op1: `${variant.op1_name}=${variant.op1_value}`,
          op2: `${variant.op2_name}=${variant.op2_value}`,
          op3: `${variant.op3_name}=${variant.op3_value}`
        },
        metafields_count: 0,
        store: storeName,
        store_id: storeId
      });
      return;
    }

    // Mark task as completed only if metafields were successfully set
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully updated ${updatedMetafields.length} metafields for variant ${variantId} on ${storeName}`,
      variantId: variantId,
      sku: variantSku,
      shopify_variant_id: shopifyVariant.variantId,
      product_title: shopifyVariant.productTitle,
      metafields_count: updatedMetafields.length,
      metafields: updatedMetafields.map(mf => ({ namespace: mf.namespace, key: mf.key, value: mf.value })),
      store: storeName,
      store_id: storeId
    });

    console.log(`[processUpdateShopifyVariantMetafieldsTask] Successfully completed metafields update for variant ${variantId} on ${storeName}`);

  } catch (error) {
    console.error(`[processUpdateShopifyVariantMetafieldsTask] Error processing task ${task.id}:`, error);
    await updateTaskStatus(task.id, 'error', {
      message: `Error updating variant metafields: ${error.message}`,
      error: error.message,
      stack: error.stack
    });
  }
}
